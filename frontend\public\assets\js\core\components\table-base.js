/*
 * Base Table Class
 * Provides common functionality for all WET-BOEW DataTables implementations
 * Follows GC Web Standards and eliminates code duplication
 */
import { showMessage } from '../helpers/message-helpers.js';
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { buildTableMessageConfig, buildTableHtml, initializeWETTable } from '../helpers/table-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

export class BaseTable {
    constructor(config) {
        this.containerId = config.containerId;
        this.tableId = config.tableId;
        this.entityConfig = config.entityConfig;
        this.messageConfigMaps = config.messageConfigMaps;
    }

    // Standard table creation flow with simple configuration override
    async create(config = {}) {
        // Simple configuration override - update properties if provided
        if (config.containerId) this.containerId = config.containerId;
        if (config.tableId) this.tableId = config.tableId;
        if (config.userInfo) this.userInfo = config.userInfo;

        const $container = $(`#${this.containerId}`);

        if (!$container.length) {
            console.warn(`Table container not found: ${this.containerId}`);
            throw new Error('Container not found');
        }

        try {
            // Show loading state
            this.showLoadingState($container);

            // Fetch data (implemented by subclasses)
            const data = await this.fetchData();

            // Handle empty data
            if (!data || data.length === 0) {
                this.showEmptyState($container);
                return;
            }

            // Build and display table
            const tableHtml = this.buildTable(data);
            $container.html(tableHtml);

            // Initialize WET-BOEW DataTables
            await initializeWETTable(this.tableId);

            // Post-initialization setup (optional, implemented by subclasses)
            this.postInitialize();

        } catch (error) {
            this.handleError($container, error);
            throw error;
        }
    }

    // Show loading state with accessibility
    showLoadingState($container) {
        const loadingText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.common.loading }, 'message');
        $container.html(`<p role="status" aria-live="polite">${loadingText}...</p>`);
    }

    // Show empty state with localized message
    showEmptyState($container) {
        const entityTerm = getLocalizedText({ message: this.entityConfig.entityTerms }, 'message');
        const emptyTableTemplate = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable }, 'message');
        const emptyTableText = emptyTableTemplate.replace('{entity}', entityTerm);
        $container.html(`<div class="alert alert-info"><p>${emptyTableText}</p></div>`);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }

    // Build standard WET-BOEW table HTML using table-helpers
    buildTable(data) {
        const messageConfig = buildTableMessageConfig(this.entityConfig.entityTerms);
        const headers = this.getTableHeaders();
        const rows = this.buildTableRows(data);

        // Build header cells
        const headerCells = Object.values(headers).map(header =>
            `<th scope="col">${header}</th>`
        ).join('');

        // Use buildTableHtml from table-helpers to eliminate duplication
        return buildTableHtml({
            tableId: this.tableId,
            headerCells,
            tableRows: rows,
            messageConfig,
            tableOptions: this.getTableOptions()
        });
    }

    // Abstract methods to be implemented by subclasses
    async fetchData() {
        throw new Error('fetchData() must be implemented by subclass');
    }

    getTableHeaders() {
        throw new Error('getTableHeaders() must be implemented by subclass');
    }

    buildTableRows(data) {
        throw new Error('buildTableRows() must be implemented by subclass');
    }

    // Optional methods that can be overridden
    getTableOptions() {
        return {}; // Default empty options
    }

    postInitialize() {
        // Optional post-initialization logic
    }
}

// Specialized base class for read-only tables
export class ReadOnlyTable extends BaseTable {
    constructor(config) {
        super(config);
    }

    // Read-only tables typically don't need post-initialization
    postInitialize() {
        // No additional setup needed for read-only tables
    }
}

// Specialized base class for tables with bulk checkbox operations
export class BulkCheckboxTable extends BaseTable {
    constructor(config) {
        super(config);
        this.hasChanges = false;
    }

    // Bulk checkbox tables need event handlers setup
    postInitialize() {
        this.initializeEventHandlers();
    }

    // Abstract method for event handlers
    initializeEventHandlers() {
        // To be implemented by subclasses that need event handling
    }

    // Common change tracking functionality
    trackChanges(hasChanges) {
        this.hasChanges = hasChanges;
        this.toggleBulkActions();
    }

    toggleBulkActions() {
        // Default implementation - subclasses should override this
        // This method should enable/disable buttons based on this.hasChanges
        console.warn('toggleBulkActions() should be overridden by subclass');
    }
}

