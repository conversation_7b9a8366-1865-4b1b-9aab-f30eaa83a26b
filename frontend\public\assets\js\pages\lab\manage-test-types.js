/*
 * Manage Test Types Page JavaScript
 * Handles test types management with role-based access control and table display
 */
import { testTypeTableManager } from './test-type-table-manager.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN,
    onAuthSuccess: initializeManageTestTypesPage,
    onAuthFailure: showAccessDeniedError
};

// Initialize manage test types page
async function initializeManageTestTypesPage(userInfo) {
    try {
        await initializeTestTypesManagement(userInfo);
    } catch (error) {
        console.error('Manage test types page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize test types management
async function initializeTestTypesManagement(userInfo) {
    // Initialize test types table with user info for lab-specific filtering
    try {
        const tableConfig = {
            ...TEST_CONFIGS.ui.testTypeTable.manage,
            userInfo: userInfo,
            readOnlyMode: false  // Explicitly set management mode
        };
        await testTypeTableManager.create(tableConfig);
        console.log('Test types management page initialized successfully');
        return true;
    } catch (error) {
        console.error('Failed to initialize test types table:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
        return false;
    }
}

// Show access denied error for test types management
function showAccessDeniedError() {
    showMessage('#page-error-container', 'auth.messages.errors', 'accessDenied', MESSAGE_CONFIG_MAPS);

    // Redirect to home page after showing error
    // Access denied: 3s delay (user operation feedback - allows reading error message)
    setTimeout(() => {
        redirectToPageByKey('home');
    }, GLOBAL_CONFIGS.navigation.redirectDelay);
}
