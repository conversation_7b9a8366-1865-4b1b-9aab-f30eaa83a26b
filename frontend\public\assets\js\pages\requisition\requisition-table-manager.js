/*
 * Requisition Table Manager
 * Uses a sequential queue to prevent race conditions when creating multiple tables
 * Extends ReadOnlyTable with queue management for concurrent table creation
 */
import { RequisitionApi } from '../../core/services/requisition-api.js';
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { escapeHtml, formatDate, formatNotAvailable } from '../../core/helpers/format-helpers.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

class RequisitionTableManager extends ReadOnlyTable {
    constructor() {
        super({
            containerId: 'requisition-table-container',
            tableId: 'requisition-table',
            entityConfig: REQUISITION_CONFIGS.ui.table,
            messageConfigMaps: {
                global: GLOBAL_CONFIGS,
                requisition: REQUISITION_CONFIGS
            }
        });

        // Queue management for concurrent table creation
        this.initializationQueue = [];
        this.isInitializing = false;
    }

    // Override create method to add queue management
    async create(config) {
        return new Promise((resolve, reject) => {
            // Add to queue with promise handlers
            this.initializationQueue.push({
                config: config,
                resolve: resolve,
                reject: reject
            });

            // Start processing queue
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isInitializing || this.initializationQueue.length === 0) {
            return;
        }

        this.isInitializing = true;

        // Process next task in queue
        const task = this.initializationQueue.shift();

        try {
            await this.initializeTable(task.config);
            task.resolve();
            // Process next task
            this.isInitializing = false;
            this.processQueue();
        } catch (error) {
            console.error(`Failed to initialize table "${task.config.title}":`, error);
            task.reject(error);
            // Continue processing queue even if one fails
            this.isInitializing = false;
            this.processQueue();
        }
    }

    async initializeTable(config) {
        // Store current config for access by other methods
        this.currentConfig = config;

        // Use parent class create method
        return await super.create(config);
    }

    // Fetch requisitions data from API
    async fetchData() {
        const config = this.currentConfig;
        // Convert jQuery promise to native promise for async/await compatibility
        const requisitions = await new Promise((resolve, reject) => {
            RequisitionApi.getRequisitions(config.apiStatusQuery, config.labId, config.role)
                .done(resolve)
                .fail(reject);
        });

        // Backend now provides data sorted by timestamp (newest first)
        return requisitions || [];
    }

    // Get table headers based on user role
    getTableHeaders() {
        const config = this.currentConfig;
        const showLabColumn = config.role === 'scientist';

        const headers = {
            reqName: getLocalizedText({ message: REQUISITION_CONFIGS.ui.table.columns.reqName }, 'message'),
            dateModified: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.dateModified }, 'message')
        };

        if (showLabColumn) {
            headers.lab = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.lab }, 'message');
        }

        return headers;
    }

    // Build table rows from requisitions data
    buildTableRows(requisitions) {
        const config = this.currentConfig;
        const showLabColumn = config.role === 'scientist';
        const notAvailableText = formatNotAvailable();

        return requisitions.map(req => {
            // Use updated_at if available, otherwise fall back to created_at
            const dateToDisplay = req.updated_at || req.created_at;
            const formattedDate = dateToDisplay ? formatDate(dateToDisplay) : notAvailableText;

            // Build table row with conditional Lab column
            return `<tr>
                <td>${escapeHtml(req.req_name || notAvailableText)}</td>
                ${showLabColumn ? `<td>${escapeHtml(req.lab_name || notAvailableText)}</td>` : ''}
                <td>${formattedDate}</td>
            </tr>`;
        }).join('');
    }

    // Override table options for requisition-specific configuration
    getTableOptions() {
        const config = this.currentConfig;
        const showLabColumn = config.role === 'scientist';
        // Adjust DataTables ordering configuration based on column visibility
        const dateColumnIndex = showLabColumn ? 2 : 1;

        return {
            order: [[dateColumnIndex, "desc"]] // Sort by date modified descending
        };
    }

}

// Create and export a singleton instance of the manager
// This ensures that the same queue is used throughout the application
export const requisitionTableManager = new RequisitionTableManager();