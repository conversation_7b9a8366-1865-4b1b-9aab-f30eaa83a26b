/*
 * Staff Table
 * Read-only table for displaying laboratory staff information
 * Uses BaseTable for standardized WET-BOEW DataTables implementation
 */
import { LabApi } from '../../core/services/lab-api.js';
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { getRoleDisplayName, escapeHtml, formatNotAvailable } from '../../core/helpers/format-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

class StaffTable extends ReadOnlyTable {
    constructor() {
        super({
            containerId: 'staff-table-container',
            tableId: 'staff-table',
            entityConfig: { entityTerms: GLOBAL_CONFIGS.ui.table.entityTerms.staff },
            messageConfigMaps: {
                global: GLOBAL_CONFIGS
            }
        });
    }

    // Fetch staff data from API
    async fetchData() {
        // LabApi.getLabStaff() returns a jQuery promise, convert to native promise
        const staffData = await new Promise((resolve, reject) => {
            LabApi.getLabStaff()
                .done(resolve)
                .fail(reject);
        });
        return staffData || [];
    }

    // Get table headers configuration
    getTableHeaders() {
        return {
            name: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.name }, 'message'),
            email: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.email }, 'message'),
            role: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.role }, 'message')
        };
    }

    // Build table rows from staff data
    buildTableRows(staffData) {
        return staffData.map(staff => `
            <tr>
                <td>${escapeHtml(staff.email ? staff.email.split('@')[0] : 'Unknown')}</td>
                <td>${escapeHtml(staff.email || formatNotAvailable())}</td>
                <td>${getRoleDisplayName(staff.role)}</td>
            </tr>
        `).join('');
    }
}

// Create and export singleton instance
export const staffTable = new StaffTable();
