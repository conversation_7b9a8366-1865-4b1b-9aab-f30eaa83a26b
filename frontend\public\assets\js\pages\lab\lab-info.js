/*
 * Lab Information Page JavaScript
 * Handles lab information display with role-based access control
 */

import { staffTable } from './staff-table.js';
import { createTestTypeTable } from './test-type-table-manager.js';
import { formatNotAvailable } from '../../core/helpers/format-helpers.js';
import { getLabName } from './shared/lab-helpers.js';
import { showMessage } from '../../core/helpers/message-helpers.js';

import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN,
    onAuthSuccess: initializeLabInfoPage,
    onAuthFailure: showAccessDeniedError
};

// Initialize lab info page
async function initializeLabInfoPage(userInfo) {
    try {
        await loadLabInformation(userInfo);
        console.log('Lab information page initialized successfully');
    } catch (error) {
        console.error('Lab info page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Load lab information data
async function loadLabInformation(userInfo) {
    // Display basic lab information immediately
    displayBasicLabInfo(userInfo);

    // Load staff and test types tables
    await Promise.all([
        loadStaffTable(),
        loadTestTypesTable(userInfo)
    ]);
}

// Display basic lab information
function displayBasicLabInfo(userData) {
    const notAvailableText = formatNotAvailable();

    // Display lab name using lab helper
    const labName = getLabName(userData, notAvailableText);
    $('#lab-name-display').text(labName);

    // Display admin contact
    const adminEmail = userData.sub || userData.email || notAvailableText;
    $('#admin-contact-display').text(adminEmail);
}

// Load staff table using staff table manager
async function loadStaffTable() {
    const staffConfig = {
        containerId: 'staff-table-container'
    };

    try {
        await staffTable.create(staffConfig);
    } catch (error) {
        console.error('Failed to load staff table:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Load test types table in read-only mode
async function loadTestTypesTable(userInfo) {
    try {
        // Create a dedicated read-only instance
        const readOnlyTestTypeTable = createTestTypeTable({
            containerId: 'test-types-table-container',
            userInfo: userInfo,
            readOnlyMode: true  // Enable read-only mode for lab info display
        });

        await readOnlyTestTypeTable.create();
    } catch (error) {
        console.error('Failed to load test types table:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}


// Show access denied error
function showAccessDeniedError() {
    showMessage('#page-error-container', 'auth.messages.errors', 'accessDenied', MESSAGE_CONFIG_MAPS);
    setTimeout(() => {
        redirectToPageByKey('home');
    }, GLOBAL_CONFIGS.navigation.redirectDelay);
}
